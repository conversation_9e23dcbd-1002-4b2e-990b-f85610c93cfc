import {FC, useCallback, useEffect, useRef} from "react";
import {CustomFileData} from "../api/api";
import {useUser} from "../provider/UserProvider";
import toast from "react-hot-toast";
import { has3dConfig, loadScript, sanitizeCustomAsset } from "./shared/util";
import { useBrowser } from "../provider/BrowserProvider";
import { currentAssetsVersion } from "./shared/variables";
import { useAssets } from "../provider/AssetsProvider";
import { EMBED } from "./shared/types";
import { useParams } from "react-router-dom";

interface ViewerProps {
  file: CustomFileData;
  setFile?: (file: CustomFileData) => void;
  defaultSettingsMode?: boolean;
  isPlayground?: boolean;
  saveCallbackRef?: React.MutableRefObject<((data?:any) => Promise<void>) | undefined>;
}

const MiniEditor: FC<ViewerProps> = (props: ViewerProps) => {
  const editorInstance = useRef<any | null>(null);
  const canvasContainer = useRef<HTMLDivElement>(null);
  const ready = useRef<boolean>(true);
  const {api , config, user , isLogin , isEnterpriseClient} = useUser();
  const {addWebgi , setViewer } = useBrowser();
  const { getCustomAssets , handleArchiveAsset , handleUploadCustomAsset , getlogoList , handleUploadLogo } = useAssets();
  const newLogoUrl = useRef<string | null>(null);
  const urlParams = useParams();


  const loadEditorInstance = useCallback(async (container : HTMLDivElement, projectData : any, editorSettings : any ) => {
    let EditorClass;

    if(config && !config["editor-path"]) config["editor-path"] = "https://releases.ijewel3d.com/libs/mini-editor/0.1.39/bundle.iife.js";
    // if (config) config["editor-path"] = "../../../libs/bundle.iife.js";
    //custom editor
    if (config?.["editor-path"]) {
      await addWebgi();
      await loadScript(config["editor-path"]);
      EditorClass = (window as any)["ijewelEditor"]?.setupIjewelDriveEditor2;
      if (!EditorClass) {
        toast.error("ijewelEditor is not available.");
      }
    } else {
      throw new Error("No config");
    }
    const viewerInstance = (window as any)["ijewelEditor"]?.setupIjewelDriveEditor2?.({
      baseName : config["assets-path"] ? api?.getBaseName() : "packs", //if assets path is provided then use current base name, else use packs
      project : projectData,
      target : container,
      editorOptions: editorSettings,
      assetsPath : config?.["assets-path"] || undefined
    })

     window.addEventListener('webgi-viewer-ready', async (ev : any) => { //use ijewel-editor-ready in 0.1.23 and height
      const viewer = ev.detail.viewer;
      setViewer(viewer);
    });

    return viewerInstance;
  }, [addWebgi, api, config, setViewer]);

  const embedAccessStatus = useCallback(() => {
    if (!props.file.meta?.pub) {
      return {
        canEmbed: false,
        warnningMessage:
          "Private Models cannot be embedded. Please right click on the file, select share and Toggle Public Access checkbox",
      };
    } else if (!isLogin) {
      return {
        canEmbed: false,
        warnningMessage: "Please login to access this feature",
      };
    } else if (!props.file.id) {
      return {
        canEmbed: false,
        warnningMessage: "Please save the project first to access this feature",
      };
    }

    return {
      canEmbed: true,
      warnningMessage: undefined,
    };
  }, [isLogin, props.file]);

  const handleProjectLogoUpdate: EMBED["handleProjectLogoUpdate"] = useCallback(
    async ({ item, isRemove = false, isRemoveAsset = false }: { item: any; isRemove?: boolean; isRemoveAsset?: boolean; }) => {
      if (!api || !props.file) return null;
      const logoUrl = (isRemove || isRemoveAsset) ? null : item.path;

      if (isRemoveAsset) {

         //archive asset
        const asset = await handleArchiveAsset({ asset: item, isArchive: true });
        if (!asset) {
          console.error("Error archiving asset");
          return null;
        }
        
      }

      const updatedConfig = { ...item.config, logo: logoUrl };
      const { data: updatedFile, error: updateError } = await api.updateFile({ id: props.file.id, config: updatedConfig });
      if (updateError || !updatedFile) {
        console.error("Error updating file configuration", updateError);
        return null;
      }
      
      newLogoUrl.current = logoUrl;
      props.setFile && props.setFile(updatedFile);
      return sanitizeCustomAsset(updatedFile);
    } 
  , [api, handleArchiveAsset, props]);


  const initEditor = useCallback( async (modelUrl: string) => {
    if(!config) {
      console.error("No drive config found");
      return;
    }
    if(!ready.current) return;
    ready.current = false;
    let projectData: any = {};
    if(!props.file?.config){
      console.log("loading default config");
      projectData = {};
    }else{
      projectData = {...props.file.config}
    }
    const baseName = api?.getBaseName() || "";
    
    //apply default config if no 3d config found
    if(!has3dConfig(props.file) && props.file.defaultConfig && !props.defaultSettingsMode){
      props.file.defaultConfig.isDefault && delete props.file.defaultConfig.isDefault;
      projectData = {
        ...props.file.defaultConfig,
        modelUrl : modelUrl
      }
    }
      
    projectData.modelUrl = props.defaultSettingsMode ? undefined : modelUrl;
    projectData.basePath =  config["assets-path"] ? api?.getBasePath() : api?.getBasePath().replace(baseName , "packs")
    projectData.name = projectData.name ?? props.file.name;
    projectData.posterUrl = props.file.thumbnailUrl
    projectData.tags = props.file.tags?.split(",") ?? [];
    projectData.version = projectData.version ?? currentAssetsVersion;
    projectData.slug = props.file.id

   

    const customAsset = {
      getCustomAssets : getCustomAssets,
      handleUploadAsset: handleUploadCustomAsset,
      handleArchiveAsset: handleArchiveAsset
    };

    // const baseEmbed =
    //     (window.location.origin.includes("ijewel.design")
    //       ? window.location.origin
    //       : "https://ijewel.design") + "/embedded";
    const baseEmbed = new URL(`${window.location.origin}/${urlParams.basename}/files/${props.file.id}/embedded`).toString();

    const { canEmbed, warnningMessage } = embedAccessStatus();

    const isPremium = (user?.meta?.plan && user?.meta?.plan === "premium") || isEnterpriseClient()
    const isBusiness = (user?.meta?.plan && user?.meta?.plan === "business") || isEnterpriseClient()
    console.log("isPremium", isPremium , "isBusiness", isBusiness , user?.meta?.plan);
    const embedProps: EMBED = {
      baseurl: baseEmbed,
      previewBaseurl: baseEmbed + "/preview",
      handleUploadLogo: handleUploadLogo,
      handleProjectLogoUpdate: handleProjectLogoUpdate,
      getProjectCountByLogoUrl: api?.countFilesByLogo,
      getlogoList: getlogoList,
      enabled: canEmbed,
      warnningMessage: warnningMessage,
      //only premium and above can remove hologram 
      //todo: handle enterprise plan
      canRemoveHologram : isPremium || isBusiness,
    };

    const editorSettings = {
      embed: embedProps,
      customAssets: customAsset,
      showEmbedding: props.defaultSettingsMode ? false : true,
      showBrandingSetting: props.defaultSettingsMode ? false : true,
      showExport: props.defaultSettingsMode ? false : true,
      // hideSaveButton: true,
      showWelcome: props.defaultSettingsMode ? false : true,
      showDropzone: props.defaultSettingsMode,
      showProjectSettings : props.defaultSettingsMode ? false : true,
      hideLogoInput: true,
      saveCallback: props.saveCallbackRef?.current,
      save: props.saveCallbackRef?.current,
      // hidePoster: false,
      showHeader: false,
      isPremium: isPremium,
      isBusiness: isBusiness,
      // onSubscribe: (...args: any[]) => handleSubscriptionModal(...args),
      // onPosterUpdate: onViewUpdate,
      // categories: Categories,
      fullEditor: props.isPlayground,
      isLogin: true,
      showSampleModels: props.defaultSettingsMode ? false : true,
      // assetFiles: !editorInstance.current ? assets : undefined,
      token : api?.getToken(),
      useDefaultConfig: true,
      previewLink: window.location.href.replace("/edit", "/view"),
      
    };
    const container = canvasContainer.current;
    if (!container) return;
    if(!editorInstance.current) {
      const editor = await loadEditorInstance(container, projectData ,editorSettings );
      editorInstance.current = (editor);
    }else{
      editorInstance.current.render(projectData, {
        ...editorSettings,
      });
    }
    ready.current = true;
  }, [api, config, embedAccessStatus, getCustomAssets, getlogoList, handleArchiveAsset,
     handleProjectLogoUpdate, handleUploadCustomAsset, handleUploadLogo, isEnterpriseClient,
      loadEditorInstance, props.defaultSettingsMode, props.file, props.isPlayground,
       props.saveCallbackRef, urlParams.basename, user]
  );

  useEffect(() => {
    if (!props.file) return;
    if (!props.file.url) return;
    const url = props.file.url;
    initEditor(url);
    
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.file , api, editorInstance.current , user]);

 
  return (
    //   <ReactShadowRoot>
    <div className="w-full h-full isolate" >
        <div ref={canvasContainer} id="webgi-editor-wrapper" className="w-full h-full flex">
          {/* <canvas ref={canvas} id="webgi-canvas" className="w-full h-full"></canvas> */}
        </div>
    </div>
    //   </ReactShadowRoot>
  );
};

export default MiniEditor;
