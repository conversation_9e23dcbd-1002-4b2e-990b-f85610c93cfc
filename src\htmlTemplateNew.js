export const htmlTemplateNew = ({
		title = 'iJewel3D Viewer',
		base,
		options = { showCard: true, showLogo: true },
		version = '0.3.20',
	}) => `
<!DOCTYPE html>
<html lang="en" >
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>${title}</title>
</head>
<body>
<div id="root"></div>
<script src='https://releases.ijewel3d.com/libs/mini-viewer/${version}/bundle.iife.js'></script>
<script >
const baseName = ${JSON.stringify(base)};
const modelId = (new URLSearchParams(window.location.search)).get('id');
// if(!modelId) window.location = 'https://drive.ijewel3d.com/drive/';
if(!modelId) alert('No file here');

const basePath = "https://assets." + baseName + ".ijewel3d.com";

ijewelViewer.loadModelById(
modelId,                        // the model ID
baseName,
document.getElementById('root'), // the container element
${JSON.stringify(options)}
);

window.addEventListener('webgi-viewer-ready', async (ev)=>{
console.log("iJewel3D Viewer is now ready.")
const viewer = ev.detail.viewer;
});

</script>
<style>
#root{
width: 100vw;
height: 100dvh;
}
</style>

</body>
</html>
`;
