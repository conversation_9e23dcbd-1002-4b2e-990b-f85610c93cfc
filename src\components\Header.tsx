import { FC, useEffect, useState } from "react";
import { useUser } from "../provider/UserProvider";
import { Logo } from "./Logo";
import { Link, useLocation, useParams } from "react-router-dom";
import Search from "./icons/Search.tsx";
import Back from "./icons/SmallArrow.tsx";
import { Button } from "./Button.tsx";
import Home from "./icons/Home";
import { UserLimits } from './shared/limits.ts';
import HardDrive from "./icons/Drive";
import Trash from "./icons/Trash";
import Cloud from "./icons/Cloud.tsx";
import { ProfileDropdown } from "./ProfileDropDown.tsx";
import { Dropdown, DropdownTrigger, Button as NextButton } from "@nextui-org/react";
import { Search as SearchBar } from "./Search.tsx";
import BaseLink from "./BaseLink.tsx";
import { ViewType } from "../types/views.ts";
import { UserPlan } from "./shared/types.ts";

interface HeaderProp {
  onViewChange?: (view: ViewType) => void;
  currentView?: ViewType;
  className?: string;
  children?: React.ReactNode;
}

const Header: FC<HeaderProp> = ({ onViewChange, currentView , className , children }) => {
  const {user , modelsCountRef, isLogin , isEnterpriseClient} = useUser();
  const { pathname } = useLocation();
  const [isDrawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [isSearchOpen, setSearchOpen] = useState<boolean>(false);
  const [isDev, setIsDev] = useState<boolean>(!(import.meta as any).env?.PROD)
  const { basename } = useParams();
  useEffect(() => {
    setIsDev(isDev || window.location.hostname.includes("dev"));
  }, []);

  // const dropdownRef = useRef<HTMLDivElement>(null);

  const plan : UserPlan = user?.meta?.plan || "free";
  const currentMaxModelUploads = UserLimits[plan]?.maxNumberOfModels
  const storagePercentage = ((modelsCountRef.current || 0) / currentMaxModelUploads) * 100;

  const isOnFoldersPage = pathname.includes('/folders');
  const isOnViewPage = pathname.includes('/view');

  return (
    <>
      <div className={"w-full h-14 flex items-center px-4 py-3 " + className}>
        {!isSearchOpen ? 
          <>
            {isOnFoldersPage && <div className="md:hidden">
              {isDrawerOpen ? <img src="/close.svg" alt="" height={"14px"} width={"14px"} onClick={() => { setDrawerOpen(false) }} />
                : <img src="/menu.svg" alt="" onClick={() => { setDrawerOpen(true) }} />}
            </div>}

            {/* Sidebar Drawer */}
            <div
              className={`fixed top-0 left-0 h-full w-52 bg-white shadow-md z-50 transform ${isDrawerOpen ? "translate-x-0" : "-translate-x-full"
                } transition-transform duration-300 ease-in-out md:hidden flex flex-col justify-between`}
            >
              <div>
                <div className="p-4 flex justify-between items-center border-b">
                  <h2 className="text-lg font-semibold">{user?.user ?? "iJewel Company"}</h2>
                  <button
                    onClick={() => setDrawerOpen(false)}
                    className="md:hidden p-2 focus:outline-none"
                  >
                    <img src="/close.svg" alt="" height={"14px"} width={"14px"} />
                  </button>
                </div>
                <nav className="flex-1 mr-2 p-4 space-y-4">
                  <ul className="space-y-2">
                    <li>
                      <Button
                        name={
                          <div className="flex items-center gap-3">
                            <Home className={`w-5 h-5 min-w-5 ${currentView === 'home' ? 'text-white' : 'text-[#373737]'
                              }`} />
                            {<span className="text-sm">Home</span>}
                          </div>
                        }
                        color={currentView === 'home' ? 'primary' : undefined}
                        varient={currentView === 'home' ? undefined : 'light'}
                        className="justify-start rounded-3xl h-8"
                        fullWidth
                        size="lg"
                        onClick={() => { onViewChange?.('home'); setDrawerOpen(false); }}
                      />
                    </li>
                    <li>
                      <Button
                        name={
                          <div className="flex items-center gap-3">
                            <HardDrive className={`w-5 h-5 min-w-5 ${currentView === 'files' ? 'text-white' : 'text-primary'
                              }`} />
                            {<span className="text-sm">My Drive</span>}
                          </div>
                        }
                        color={currentView === 'files' ? 'primary' : undefined}
                        varient={currentView === 'files' ? undefined : 'light'}
                        className="justify-start rounded-3xl h-8"
                        fullWidth
                        size="lg"
                        onClick={() => { onViewChange?.('files'); setDrawerOpen(false); }}
                      />
                    </li>
                    <li>
                      <Button
                        name={
                          <div className="flex items-center gap-3">
                            <Trash className={`w-5 h-5 min-w-5 ${currentView === 'trash' ? 'text-white' : 'text-[#373737]'
                              }`} />
                            <span className="text-sm">Deleted files</span>
                          </div>
                        }
                        color={currentView === 'trash' ? 'primary' : undefined}
                        varient={currentView === 'trash' ? undefined : 'light'}
                        className="justify-start rounded-3xl h-8"
                        fullWidth
                        size="lg"
                        onClick={() => { onViewChange?.('trash'); setDrawerOpen(false); }}
                      />
                    </li>
                  </ul>
                </nav>
              </div>
              {!isEnterpriseClient() && modelsCountRef.current !== null && <div className="p-4 border-t">
                <div className="flex gap-1">
                  <Cloud className="w-5 h-5 min-w-5"/>
                  <p className="text-sm text-gray-600">Available Storage</p>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                  <div className={`${storagePercentage > 95 ? 'bg-danger' : storagePercentage > 75 ? 'bg-warning' : 'bg-primary'} h-2 rounded-full w-full`} style={{maxWidth: `${storagePercentage}%`}}></div>
                </div>
                <p className="text-sm mt-1">{modelsCountRef.current}/{currentMaxModelUploads} Models used</p>
              </div>}
            </div>

            {/* Overlay when Drawer is Open */}
            {isDrawerOpen && (
              <div
                className="fixed inset-0 bg-black bg-opacity-25 z-20 md:hidden"
                onClick={() => setDrawerOpen(false)}
              ></div>
            )}
            
            <Link className="h-full" to={"https://drive.ijewel3d.com/"}>
              <Logo className=" h-full ml-2 mr-2" />
            </Link>
            {isLogin && isOnFoldersPage && (
              <div className="ml-16 hidden md:block">
                <SearchBar size="sm" />
              </div>
            )}
            <div className="flex-1" />
            <div className="flex justify-end items-center gap-unit-md h-full">
              {children}
              {isLogin && !isOnViewPage && (
                <>
                  {/* <Button
                        varient="ghost"
                        className="h-unit-2xl w-unit-2xl p-2 rounded-full"
                        name={<Bell className="h-5 w-5" />}
                        onClick={() => openModal("NOTIFICATIONS")}
                      />  */}
                  {isOnFoldersPage && <Search className="md:hidden fill-black" onClick={()=>{setSearchOpen(true)}}/>}

                  <Dropdown>
                    <DropdownTrigger>
                      <NextButton
                        variant="light"
                        className="h-full min-w-min aspect-square rounded-full">
                        {/*<SettingsMenu className="h-5 w-5" />*/}
                        <div className="h-full min-w-min aspect-square rounded-full bg-gray-200 overflow-hidden">
                          <img
                            src={`https://ui-avatars.com/api/?name=${encodeURIComponent(user?.user || 'User')}&background=random`}
                            alt="Profile"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </NextButton>
                    </DropdownTrigger>
                    <ProfileDropdown />
                  </Dropdown>
                </>
              )}
            </div>
            <div className="flex justify-end gap-unit-md">
              {!isLogin && !isOnViewPage && (
                <BaseLink to={isDev && !basename ? "drive-dev/login" : "/login"} state={{ redirect: pathname }}>
                  <NextButton
                    variant="solid"
                    color="primary"
                    // onClick={goToLogin}
                    children={<span className="text-sm font-semibold">Login</span>}
                    className="h-fit w-fit px-3 md:px-4 py-1.5 rounded-full"
                  />
                </BaseLink>
              )}
            </div>
          </>
        :<>
          {isLogin && isOnFoldersPage && (
              <div className="z-30 flex items-center gap-2 w-full pb-2">
                <Back onClick={()=>{ setSearchOpen(false) }} className="flex-shrink-0"/>
                <div className="flex-1">
                  <SearchBar size="lg" />
                </div>
                <div className="ml-2 flex-shrink-0">
                  <Search onClick={()=>{ setSearchOpen(false) }}/>
                </div>
              </div>
            )}
        </>
        }
      </div>
    </>
  );
};

export { Header };
