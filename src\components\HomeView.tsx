import { FC } from 'react';
import PremiumDrive from "./icons/PremiumDrive.tsx";
import Info from "./icons/Info.tsx";
import BatchX from "./icons/BatchX.tsx";
import Studio from "./icons/Studio.tsx";
import Design from "./icons/Design.tsx";
import LicensePortal from "./icons/LicensePortal.tsx";
import Play from "./icons/Play.tsx";
import { useCustomFileMap } from '../hooks/fileHooks';
import { CustomFileData } from '../api/api';
import { useModal } from '../provider/useModal';
import { useUser } from '../provider/UserProvider.ts';
import { UserPlan } from './shared/types.ts';
import BaseLink from './BaseLink.tsx';
import { Button } from '@nextui-org/react';
import BlueTick from './icons/BlueTick.tsx';
import { UserLimits } from './shared/limits.ts';

interface Tutorial {
  title: string;
  thumbnail: string;
  link: string;
  videoId: string;
}

interface Product {
  name: string;
  icon: any;
  link: string;
}

const HomeView: FC = () => {
  const { getRecentFiles } = useCustomFileMap();
  const recentFiles = getRecentFiles();
  // const storageUsed = 20.51;

  const { openModal } = useModal();
  const { user , modelsCountRef , isEnterpriseClient } = useUser();

  const tutorials: Tutorial[] = [
    {
      title: "Introduction to iJewel Drive",
      thumbnail: `https://i.ytimg.com/vi/eE40U3163KY/maxresdefault.jpg`,
      videoId: "eE40U3163KY",
      link: "https://www.youtube.com/watch?v=eE40U3163KY"
    },
    // {
    //   title: "Updating New Materials",
    //   thumbnail: `https://img.youtube.com/vi/mMHp07fiEQA/maxresdefault.jpg`,
    //   videoId: "mMHp07fiEQA",
    //   link: "https://www.youtube.com/watch?v=mMHp07fiEQA"
    // },
    // {
    //   title: "iJewel Drive Editor Overview",
    //   thumbnail: `https://img.youtube.com/vi/XUeN2kN8sts/maxresdefault.jpg`,
    //   videoId: "XUeN2kN8sts",
    //   link: "https://www.youtube.com/watch?v=XUeN2kN8sts"
    // },
    // {
    //   title: "Uploading Custom Logo",
    //   thumbnail: `https://img.youtube.com/vi/sJsW0Fmw7wg/maxresdefault.jpg`,
    //   videoId: "sJsW0Fmw7wg",
    //   link: "https://www.youtube.com/watch?v=sJsW0Fmw7wg"
    // }
  ];

  const products: Product[] = [
    { name: "BatchX", icon: <BatchX/>, link: "https://batchx.ijewel3d.com/" },
    { name: "Studio", icon: <Studio/>, link: "https://ijewel.studio/" },
    { name: "Design", icon: <Design/>, link: "https://ijewel.design/" },
    { name: "License Portal", icon: <LicensePortal/>, link: "https://license.ijewel3d.com/" },
    { name: "Playground", icon: <img src='/icons/diamond.png' className='h-[24px]'/>, link: "https://playground.ijewel3d.com/v2/" }
  ];

  const onTutorialClick = (tutorial: Tutorial) => {
    openModal("TUTORIAL", { data: tutorial });
  };

  const plan : UserPlan = user?.meta?.plan || "free";
  const nextPlan : UserPlan = plan === "free" ? "start-up" : plan === "start-up" ? "premium" : plan === "premium" ? "business" : "enterprise";
  // const currentMaxFileSize = UserLimits[plan].maxFileSizeMb;
  // const currentBandwidth = UserLimits[plan].bandwithPerMonthGb || 'Custom';
  const currentMaxModelUploads = UserLimits[plan]?.maxNumberOfModels
  const storagePercentage = ((modelsCountRef.current || 0) / currentMaxModelUploads) * 100;

  const nextMaxFileSize = UserLimits[nextPlan].maxFileSizeMb;
  const nextBandwidth = UserLimits[nextPlan].bandwithPerMonthGb || 'Custom';
  const nextModelUploads = UserLimits[nextPlan]?.maxNumberOfModels;

  return (
    <div className="p-4 space-y-5 overflow-y-auto bg-[#FFFFFF] rounded-2xl h-full">
      <h2 className="text-xl font-semibold text-gray-800 ml-3">Your Drive</h2>
      <div className="md:ml-3 ">
        <div className="flex gap-4 flex-col lg:flex-row">
          {modelsCountRef.current !== null && !isEnterpriseClient() && <div className="bg-[#FFFFFF] p-4 rounded-[20px] border border-[#E5E7EB] lg:w-[248px] h-[170px] overflow-hidden">
            <div className="flex flex-row items-center gap">
                <PremiumDrive className="w-7 h-7 mr-[7px] text-primary" color={
                  plan === "free" ? "#373737" : plan === "start-up" ||  plan === "premium" ? "currentColor" : undefined
                }/>
                <div className="flex items-center gap-2 w-full">
                  <h2 className="text-[20px] font-medium text-[#111827] pl-1">
                    <span className='capitalize font-semibold'>{plan} plan</span>
                  </h2>
                  <BaseLink to={"/pricing"}><Info className="w-5 h-5 text-[#9CA3AF]"/></BaseLink>
                </div>
              <div className='flex-1'></div>
            </div>
            <h2 className="text-[20px] font-medium text-[#111827] mt-4 text-md mb-1">Drive storage</h2>
            <div className="mb-2">
              <span className="text-[14px] font-semibold text-[#111827]">{modelsCountRef.current} Models</span>
              <span className="text-[#6B7280] text-[14px]"> of {currentMaxModelUploads}</span>
            </div>
              {/* <div className="bg-[#6E72F2] h-[3px] rounded-full" style={{ width: `${(modelsCountRef.current/currentMaxFileSize) * 100}%` }}></div> */}
                <div className="h-1 bg-[#C0C0C0] rounded-3xl overflow-hidden mt-5 mr-2">
                  <div
                    className={`h-full rounded-full ${storagePercentage > 95 ? 'bg-danger' : storagePercentage > 75 ? 'bg-warning' : 'bg-primary'}`}
                    style={{width: `${storagePercentage}%`}}
                  />
                {/* </div */}
            </div>
          </div>}

          {!isEnterpriseClient() && <div className="bg-[#FFFFFF] p-[10px] rounded-[20px] border border-[#E5E7EB] lg:w-[690px] min-h-[170px]">
            <div className="flex flex-col md:flex-row gap-unit-xl">
              <div className="flex flex-col gap-3">
                <p className="text-[14px] font-semibold text-[#686868]">Special for you</p>
                <div className="flex flex-row items-center gap">
                    <PremiumDrive className="w-7 h-7 mr-[7px] text-primary" color={
                      nextPlan === "start-up" ||  nextPlan === "premium" ? "currentColor" : undefined
                    }/>
                    <div className="flex items-center gap-2 w-full">
                      <h2 className="text-[20px] font-medium text-[#111827] pl-1">
                        <span className="font-semibold">iJewel Drive </span>
                        <span className='capitalize'>{nextPlan}</span>
                      </h2>
                      <BaseLink to={"/pricing"}><Info className="w-5 h-5 text-[#9CA3AF]"/></BaseLink>
                    </div>
                  <div className='flex-1'></div>
                </div>
                {/* <div className="flex items-center gap-2">
     
                </div> */}
                <p className="text-[12px] leading-[15px] text-[#4B5563] pl-1">
                  Secure cloud storage for 3D files to easily store, sync, and share models safely.
                </p>
                <BaseLink to={"/pricing"}>
                    <Button
                      fullWidth={false}
                      color='secondary'
                      className='w-unit-5xl h-unit-2xl'
                      size='md'
                    >
                        Upgrade
                    </Button>
                  </BaseLink>
              </div>

              <div className="flex flex-col">
                <div className="bg-[#F6F6F6] rounded-xl">
                  <div className="flex items-center gap-2 h-[32px] w-[214px] pl-2.5">
                    <span className="text-[#6E72F2] text-[12px]"><BlueTick/></span>
                    <span className="text-[#4B5563] text-[12px]">{nextMaxFileSize} Mb file size limit</span>
                  </div>
                </div>
                <div className="rounded-xl">
                  <div className="flex items-center gap-2 h-[32px] w-[214px] pl-2.5">
                    <span className="text-[#6E72F2] text-[12px]"><BlueTick/></span>
                    <span className="text-[#4B5563] text-[12px]">{nextBandwidth} bandwidth GB / month</span>
                  </div>
                </div>
                <div className="bg-[#F6F6F6] rounded-xl">
                  <div className="flex items-center gap-2 h-[32px] w-[214px] pl-2.5">
                    <span className="text-[#6E72F2] text-[12px]"><BlueTick/></span>
                    <span className="text-[#4B5563] text-[12px]">{nextModelUploads} model uploads</span>
                  </div>
                </div>
                {nextPlan !== "start-up" && <div className="rounded-xl">
                  <div className="flex items-center gap-2 h-[32px] w-[214px] pl-2.5">
                    <span className="text-[#6E72F2] text-[12px]"><BlueTick/></span>
                    <span className="text-[#4B5563] text-[12px]">Custom logo</span>
                  </div>
                </div>}
              </div>
            </div>}
          </div>
        </div>
      </div>

      {/* Recent Files Section */}
      <div className="md:ml-3">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Recent Files</h2>
        <div className="flex gap-4">
          {recentFiles.length === 0 ? (
            <p className="text-gray-500 text-sm">No recent files</p>
          ) : (
            recentFiles.slice(0, 6).map((file: CustomFileData, index: number) => (
              <div
                key={index}
                className="cursor-pointer group"
                onClick={() => {
                  openModal("PREVIEW", { file: file });
                }}
              >
                <div className="relative border bg-[#F9FAFB] rounded-t-[10px] w-[170px] h-[100px] overflow-hidden">
                  <img
                    src={file.thumbnailUrl || "/placeholder.webp"}
                    alt={file.name}
                    onError={(e)=>(e.currentTarget as HTMLImageElement).src = "/placeholder.webp"}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="h-[32px] w-[170px] border rounded-b-[10px] bg-white flex items-center justify-start px-2">
                  <p className="text-[14px] text-gray-800 line-clamp-1 font-[400]">{file.name}</p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Tutorials Section */}
      <div className="md:ml-3">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Tutorials</h2>
        <div className="flex gap-6 flex-wrap md:flex-nowrap">
          {tutorials.map((tutorial, index) => (
            <div 
              key={index} 
              onClick={() => onTutorialClick(tutorial)}
              className="md:w-[200px] cursor-pointer w-full"
            >
              <div className="relative overflow-hidden bg-[#F9FAFB]">
                <div className="md:h-[100px] h-[190px]">
                  <img 
                    src={tutorial.thumbnail} 
                    alt={tutorial.title}
                    className="w-full h-full object-cover rounded-t-[10px] bg-black/5 group-hover:bg-black/20 transition-colors"
                  />
                  <div className="absolute inset-0">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="rounded-full flex items-center justify-center">
                        <Play className="w-14 h-14 text-gray-800 mb-3" />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="h-[32px] border rounded-b-[10px] bg-white flex items-center justify-start pl-1">
                  <p className="text-[14px] text-gray-800 line-clamp-1 font-[400]">{tutorial.title}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Products Section */}
      <div className="md:ml-3">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">iJewel Products</h2>
        <div className="flex gap-2 md:flex-auto flex-wrap">
          {products.map((product, index) => (
            <a
              key={index}
              href={product.link}
              target="_blank"
              rel="noopener noreferrer"
              className="flex flex-col h-[70px] px-[20px] gap-unit-sm justify-center items-center bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border-[1px]"
            >
              {product.icon}
              <span className="text-sm text-gray-800">{product.name}</span>
            </a>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HomeView; 